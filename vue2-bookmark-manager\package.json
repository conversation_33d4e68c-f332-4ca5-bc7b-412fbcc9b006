{"name": "vue2-bookmark-manager", "version": "1.0.0", "description": "Vue2 Bookmark Manager Frontend", "main": "src/main.js", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.6.0", "echarts": "^5.4.3", "vue": "^2.6.14", "vue-router": "^3.6.5", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-service": "^5.0.8", "autoprefixer": "^10.4.21", "babel-eslint": "^10.1.0", "eslint": "^8.0.0", "eslint-plugin-vue": "^9.0.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}