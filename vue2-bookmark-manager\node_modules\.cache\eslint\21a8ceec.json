[{"D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\src\\main.js": "1", "D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\src\\App.vue": "2", "D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\src\\components\\FilterSidebar.vue": "3", "D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\src\\components\\BookmarkCard.vue": "4", "D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\src\\components\\StatsChart.vue": "5", "D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\src\\components\\BookmarkForm.vue": "6", "D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\src\\utils\\helpers.js": "7", "D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\src\\services\\api.js": "8", "D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\src\\utils\\constants.js": "9"}, {"size": 218, "mtime": 1752596352000, "results": "10", "hashOfConfig": "11"}, {"size": 24912, "mtime": 1752596342000, "results": "12", "hashOfConfig": "11"}, {"size": 11746, "mtime": 1752596190000, "results": "13", "hashOfConfig": "11"}, {"size": 4151, "mtime": 1752596094000, "results": "14", "hashOfConfig": "11"}, {"size": 7549, "mtime": 1752596230000, "results": "15", "hashOfConfig": "11"}, {"size": 8948, "mtime": 1752596138000, "results": "16", "hashOfConfig": "11"}, {"size": 5348, "mtime": 1752596064000, "results": "17", "hashOfConfig": "11"}, {"size": 3458, "mtime": 1752596016000, "results": "18", "hashOfConfig": "11"}, {"size": 2293, "mtime": 1752596034000, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "k9ujoq", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\src\\main.js", ["47"], [], "D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\src\\App.vue", ["48"], [], "D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\src\\components\\FilterSidebar.vue", ["49"], [], "D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\src\\components\\BookmarkCard.vue", ["50"], [], "D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\src\\components\\StatsChart.vue", ["51"], [], "D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\src\\components\\BookmarkForm.vue", ["52"], [], "D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\src\\utils\\helpers.js", ["53"], [], "D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\src\\services\\api.js", ["54"], [], "D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\src\\utils\\constants.js", ["55"], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "56", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "56", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "56", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "56", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "56", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "56", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "56", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "56", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "56", "nodeType": null}, "Parsing error: require() of ES Module D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\node_modules\\eslint-scope\\lib\\definition.js from D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\node_modules\\babel-eslint\\lib\\require-from-eslint.js not supported.\nInstead change the require of definition.js in D:\\Cata_Dev_4060\\_src_Catai\\Catai_Bookmark\\bookmark_manager_api\\vue2-bookmark-manager\\node_modules\\babel-eslint\\lib\\require-from-eslint.js to a dynamic import() which is available in all CommonJS modules."]